// lib/tools/pdf-generator.ts

import { Buffer } from 'node:buffer';
import { storageTool, SavePdfToByteStoreResult } from './storage-tool';
import { markdownRendererTool } from './markdown-renderer-tool';
import { ChartConfig, CHART_TYPES } from './chart-tool';

/**
 * Interface for PDF content
 */
export interface PdfContent {
  title: string;
  content: string;
}

/**
 * Interface for PDF generation options
 */
export interface PdfGenerationOptions {
  title?: string;
  subtitle?: string;
  date?: string;
  logoPath?: string;
  margin?: number;
  size?: string;
  includeCover?: boolean;
  includeToc?: boolean;
  saveToByteStore?: boolean;
  category?: string;
  agentId?: string;
  agentName?: string;
  contents?: PdfContent[];
  // Additional properties that might be used as metadata
  generatedAt?: string;
  generatedBy?: string;
  documentType?: string;
  tags?: string[];
  version?: string;
  author?: string;
  department?: string;
  status?: string;
  priority?: string;
  dueDate?: string;
  relatedDocuments?: string[];
  // Analytics report specific properties
  reportId?: string;
  reportType?: string;
  // Strategy specific properties
  strategyId?: string;
  // Research specific properties
  queryId?: string;
  subTaskId?: string;
}

/**
 * PDF Generator Tool for creating PDFs from formatted content
 */
export class PdfGeneratorTool {

  /**
   * Set font safely with fallback options
   * @private
   */
  private setFontSafe(doc: any, family: string, style: string): void {
    try {
      // Try the requested font first
      doc.setFont(family, style);
    } catch (error) {
      // Fallback to safe font combinations
      const safeFamily = 'helvetica'; // A universally available font
      if (style === 'italic' || style === 'oblique') {
        doc.setFont(safeFamily, 'italic');
      } else if (style === 'bold') {
        doc.setFont(safeFamily, 'bold');
      } else {
        doc.setFont(safeFamily, 'normal');
      }
    }
  }

  /**
   * Layout helpers
   */
  private getPageSize(doc: any): { width: number; height: number } {
    return {
      width: doc.internal.pageSize.getWidth(),
      height: doc.internal.pageSize.getHeight(),
    };
  }
  private getMargins() {
    // Increased bottom margin to create a dedicated footer area
    return { left: 20, right: 20, top: 20, bottom: 30 };
  }
  private getContentWidth(doc: any): number {
    const { width } = this.getPageSize(doc);
    const m = this.getMargins();
    return width - m.left - m.right;
  }
  private getContentBottomY(doc: any): number {
    const { height } = this.getPageSize(doc);
    const m = this.getMargins();
    // Content should stop well before the page bottom to leave space for the footer
    return height - m.bottom;
  }
  private ensureSpace(doc: any, y: number, needed: number): number {
    const m = this.getMargins();
    const limit = this.getContentBottomY(doc);
    if (y + needed > limit) {
      doc.addPage();
      return m.top;
    }
    return y;
  }

  /**
   * Generate a PDF from formatted content
   * @param contents - Array of content objects with title and content properties
   * @param options - PDF generation options
   * @returns PDF buffer
   */
  async generatePdf(contents: PdfContent[], options: PdfGenerationOptions = {}): Promise<Buffer | SavePdfToByteStoreResult> {
    try {
      // Dynamic import for jsPDF to handle server-side environment
      const { jsPDF } = await import('jspdf');

      // Initialize jsPDF instance for PDF generation
      const doc = new jsPDF();
      const initialY = this.getMargins().top;
      let yPosition = initialY;


      // Add cover page if requested
      if (options.includeCover !== false) {
        yPosition = this.addCoverPage(doc, contents, options, yPosition);
      }

      // Add table of contents if requested
      if (options.includeToc !== false) {
        doc.addPage();
        yPosition = initialY;
        yPosition = this.addTableOfContents(doc, contents, options, yPosition);
      }

      // Add content pages
      for (const content of contents) {
        // Process markdown content using the MarkdownRendererTool
        const processedContent = {
          title: content.title,
          content: markdownRendererTool.markdownToPdfFormat(content.content)
        };

        doc.addPage();
        yPosition = initialY;
        yPosition = this.addContentPage(doc, processedContent, options, yPosition);
      }

      // Add page numbers to all pages
      this.addPageNumbers(doc);

      // Convert PDF to ArrayBuffer and then to Node Buffer
      const pdfArrayBuffer = doc.output("arraybuffer");
      const pdfBuffer = Buffer.from(pdfArrayBuffer);

      // If saveToByteStore is true, save to byteStore
      if (options.saveToByteStore) {
        // Combine all content into a single string for vector search
        const combinedContent = contents.map(content =>
          `${content.title}\n\n${markdownRendererTool.markdownToPlainText(content.content)}`
        ).join('\n\n');

        const title = options.title || (contents.length > 0 ? contents[0].title : 'Untitled Document');
        const category = options.category || 'Marketing Agent Team';

        const metadata: Record<string, string | string[] | undefined> = {
          generatedAt: new Date().toISOString(),
          generatedBy: options.agentName || 'Marketing Agent',
          agentId: options.agentId || 'unknown',
          pmoAgentId: (options as any).pmoAgentId,
          elevenLabsAgentId: (options as any).elevenLabsAgentId,
          documentType: options.documentType,
          author: options.author,
          department: options.department,
          status: options.status,
          priority: options.priority,
          dueDate: options.dueDate,
          version: options.version,
          reportId: options.reportId,
          reportType: options.reportType,
          strategyId: options.strategyId,
          queryId: options.queryId,
          subTaskId: options.subTaskId,
          tags: Array.isArray(options.tags) ? options.tags.join(',') : undefined,
          relatedDocuments: Array.isArray(options.relatedDocuments) ? options.relatedDocuments.join(',') : undefined
        };

        // Filter out undefined values
        const cleanedMetadata: Record<string, string> = {};
        Object.entries(metadata).forEach(([key, value]) => {
          if (value !== undefined) {
            cleanedMetadata[key] = String(value);
          }
        });

        return await storageTool.savePdfToByteStore(
          pdfBuffer,
          title,
          combinedContent,
          category,
          cleanedMetadata
        );
      }

      return pdfBuffer;
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw error;
    }
  }

  /**
   * Add a cover page to the PDF
   * @private
   */
  private addCoverPage(doc: any, _contents: PdfContent[], options: PdfGenerationOptions, startY: number): number {
    let yPosition = startY;
    const pageWidth = this.getPageSize(doc).width;

    const title = options.title || "Generated Document";
    doc.setFontSize(18);
    this.setFontSafe(doc, "helvetica", "bold");
    doc.text(title, pageWidth / 2, yPosition, { align: 'center' });
    yPosition += 20;

    if (options.subtitle) {
      doc.setFontSize(14);
      this.setFontSafe(doc, "helvetica", "normal");
      doc.text(options.subtitle, pageWidth / 2, yPosition, { align: 'center' });
      yPosition += 10;
    }

    const date = options.date || new Date().toLocaleDateString();
    doc.setFontSize(12);
    const dateText = `Generated on: ${date}`;
    doc.text(dateText, pageWidth / 2, yPosition, { align: 'center' });

    return yPosition;
  }

  /**
   * Add table of contents to the PDF
   * @private
   */
  private addTableOfContents(doc: any, contents: PdfContent[], _options: PdfGenerationOptions, startY: number): number {
    let yPosition = startY;
    const m = this.getMargins();
    const pageWidth = this.getPageSize(doc).width;

    doc.setFontSize(14);
    this.setFontSafe(doc, "helvetica", "bold");
    const tocTitle = 'Table of Contents';
    doc.text(tocTitle, pageWidth / 2, yPosition, { align: 'center' });
    yPosition += 15;

    doc.setFontSize(11);
    this.setFontSafe(doc, "helvetica", "normal");
    contents.forEach((content, index) => {
      yPosition = this.ensureSpace(doc, yPosition, 10);
      const tocEntry = `${index + 1}. ${content.title || 'Untitled Section'}`;
      doc.text(tocEntry, m.left, yPosition);
      yPosition += 10;
    });

    return yPosition;
  }

  /**
   * Add page numbers to all pages in the PDF
   * @private
   */
  private addPageNumbers(doc: any): void {
    const totalPages = doc.internal.getNumberOfPages();
    const { width, height } = this.getPageSize(doc);
    // Position page number comfortably inside the bottom margin area.
    const footerY = height - 15;

    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      this.setFontSafe(doc, "helvetica", "normal");
      doc.setTextColor(128, 128, 128);

      const pageText = `Page ${i} of ${totalPages}`;
      doc.text(pageText, width / 2, footerY, { align: 'center' });

      doc.setTextColor(0, 0, 0); // Reset text color
    }
  }

  /**
   * Add a content page to the PDF
   * @private
   */
  private addContentPage(doc: any, content: PdfContent, _options: PdfGenerationOptions, startY: number): number {
    let yPosition = startY;
    const m = this.getMargins();

    doc.setFontSize(14);
    this.setFontSafe(doc, "helvetica", "bold");
    const titleLines = doc.splitTextToSize(content.title || 'Untitled Section', this.getContentWidth(doc));
    doc.text(titleLines, m.left, yPosition);
    yPosition += titleLines.length * 8;
    yPosition += 12;

    this.setFontSafe(doc, "helvetica", "normal");
    doc.setFontSize(10);

    // Check for chart data first
    if (content.content.includes('<chart-data>')) {
      const chartDataMatch = content.content.match(/<chart-data>([\s\S]*?)<\/chart-data>/);
      if (chartDataMatch && chartDataMatch[1]) {
        try {
          const chartConfig = JSON.parse(chartDataMatch[1]) as ChartConfig;
          return this.renderChartInPdf(doc, chartConfig, content.title || 'Chart', yPosition);
        } catch (error) {
          console.error('Error processing chart data:', error);
          const errorText = 'Chart visualization could not be rendered due to a data error.';
          const textLines = doc.splitTextToSize(errorText, this.getContentWidth(doc));
          doc.text(textLines, m.left, yPosition);
          yPosition += textLines.length * 7;
        }
      }
    }

    // Process content using the robust HTML content renderer
    return this.addHtmlFormattedContent(doc, content.content, yPosition);
  }

  /**
   * Process HTML-formatted content from the enhanced markdown renderer.
   * This method handles various HTML tags for styling and structure.
   * @private
   */
  private addHtmlFormattedContent(doc: any, content: string, startY: number): number {
    let yPosition = startY;
    const m = this.getMargins();
    const contentWidth = this.getContentWidth(doc);
    const contentLines = content.split('\n');
    let inPre = false;
    let preBuffer: string[] = [];

    for (const line of contentLines) {
      yPosition = this.ensureSpace(doc, yPosition, 10); // Ensure some space for the next line

      // Handle <pre> blocks for code
      if (inPre) {
        preBuffer.push(line);
        if (line.includes('</pre>')) {
          const preJoined = preBuffer.join('\n');
          let codeText = preJoined.replace(/<pre[^>]*>/, '').replace(/<\/pre>/, '');
          codeText = codeText.replace(/&amp;/g, '&').replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&quot;/g, '"').replace(/&#39;/g, "'");

          this.setFontSafe(doc, 'courier', 'normal');
          doc.setFontSize(8);
          const lineHeight = 5;
          const codeLines = doc.splitTextToSize(codeText, contentWidth - 8);
          const boxHeight = codeLines.length * lineHeight + 8;
          yPosition = this.ensureSpace(doc, yPosition, boxHeight + 4);

          doc.setFillColor(248, 248, 248);
          doc.setDrawColor(220, 220, 220);
          doc.roundedRect(m.left, yPosition, contentWidth, boxHeight, 3, 3, 'FD');
          doc.text(codeLines, m.left + 4, yPosition + 6);
          yPosition += boxHeight + 4;

          this.setFontSafe(doc, 'helvetica', 'normal');
          doc.setFontSize(10);
          inPre = false;
          preBuffer = [];
        }
        continue;
      }
      if (line.includes('<pre')) {
        inPre = true;
        preBuffer = [line];
        if (line.includes('</pre>')) { // Handle single-line pre blocks
             inPre = false; // Trigger processing on next iteration
        } else {
             continue;
        }
      }

      // Handle headings
      const headingMatch = line.match(/<h([1-6])[^>]*>(.*?)<\/h[1-6]>/);
      if (headingMatch) {
        const level = parseInt(headingMatch[1]);
        const text = headingMatch[2].replace(/<[^>]*>/g, '');
        const fontSize = Math.max(10, 18 - (level * 1.5));
        const spacing = level <= 2 ? 10 : 5;

        yPosition += spacing;
        yPosition = this.ensureSpace(doc, yPosition, fontSize);
        doc.setFontSize(fontSize);
        this.setFontSafe(doc, "helvetica", "bold");
        const headingLines = doc.splitTextToSize(text, contentWidth);
        doc.text(headingLines, m.left, yPosition);
        yPosition += headingLines.length * fontSize * 0.7;
        yPosition += spacing;

        this.setFontSafe(doc, "helvetica", "normal");
        doc.setFontSize(10);
        continue;
      }
      
      // Handle empty lines as paragraph breaks
      if (line.trim() === '') {
        yPosition += 5;
        continue;
      }

      // Default: render any other line with formatting
      yPosition = this.renderHtmlLine(doc, line, yPosition);
    }
    return yPosition;
  }
  
  /**
   * Renders a single line of HTML-formatted text, handling inline tags
   * like <strong>, <em>, <code> and ensuring proper word wrapping.
   * @private
   */
  private renderHtmlLine(doc: any, line: string, startY: number): number {
    let y = startY;
    const m = this.getMargins();
    let x = m.left;
    const pageRightX = m.left + this.getContentWidth(doc);
    const lineHeight = 7; // Standard line height for 10pt font
    const spaceWidth = doc.getTextWidth(' ');

    // Set default font for the line
    this.setFontSafe(doc, 'helvetica', 'normal');
    doc.setFontSize(10);

    y = this.ensureSpace(doc, y, lineHeight); // Ensure space for at least one line

    // Split the line into text parts and html tags
    const parts = line.split(/(<[^>]+>)/g).filter(p => p);

    for (const part of parts) {
      if (part.startsWith('<')) {
        // It's a tag, adjust font style
        if (part === '<strong>') this.setFontSafe(doc, 'helvetica', 'bold');
        else if (part === '</strong>') this.setFontSafe(doc, 'helvetica', 'normal');
        else if (part === '<em>') this.setFontSafe(doc, 'helvetica', 'italic');
        else if (part === '</em>') this.setFontSafe(doc, 'helvetica', 'normal');
        else if (part === '<code>') {
            this.setFontSafe(doc, 'courier', 'normal');
            doc.setFontSize(9);
        } else if (part === '</code>') {
            this.setFontSafe(doc, 'helvetica', 'normal');
            doc.setFontSize(10);
        }
        continue;
      }

      // It's a text part, process word by word for wrapping
      const words = part.split(/\s+/).filter(w => w);
      for (const word of words) {
        const wordWidth = doc.getTextWidth(word);
        
        // Check if the word fits on the current line.
        if (x > m.left && x + wordWidth > pageRightX) {
          // Word doesn't fit, wrap to a new line.
          x = m.left;
          y += lineHeight;
          y = this.ensureSpace(doc, y, lineHeight);
        }
        
        // A single word might be wider than the whole line
        if (wordWidth > this.getContentWidth(doc)) {
            const wrappedWord = doc.splitTextToSize(word, this.getContentWidth(doc));
            doc.text(wrappedWord, x, y);
            y += (wrappedWord.length -1) * lineHeight;
        } else {
             doc.text(word, x, y);
             x += wordWidth + spaceWidth;
        }
      }
    }
    
    // After the entire logical line is rendered, advance y for the next line of content
    return y + lineHeight;
  }
  
  /**
   * This is a fallback renderer for plain markdown without HTML tags.
   * It is kept for compatibility but the main renderer is addHtmlFormattedContent.
   * @private
   */
  private addPlainMarkdownContent(doc: any, content: string, startY: number): number {
    let yPosition = startY;
    const m = this.getMargins();
    const contentWidth = this.getContentWidth(doc);
    const contentLines = (content || '').split('\n');

    for (const line of contentLines) {
        yPosition = this.ensureSpace(doc, yPosition, 7);
        if (line.trim() === '') {
            yPosition += 5; // Paragraph break
            continue;
        }
        const textLines = doc.splitTextToSize(line, contentWidth);
        doc.text(textLines, m.left, yPosition);
        yPosition += textLines.length * 7;
    }
    return yPosition;
  }

  /**
   * Renders a textual representation of a chart in the PDF.
   * NOTE: This does not render a graphical chart image. It parses the chart
   * data and displays it as formatted text, providing a summary.
   * @private
   */
  private renderChartInPdf(doc: any, chartConfig: ChartConfig, title: string, yPosition: number): number {
    const m = this.getMargins();
    let y = yPosition;

    try {
      doc.setFontSize(14);
      this.setFontSafe(doc, 'helvetica', 'bold');
      y = this.ensureSpace(doc, y, 10);
      doc.text(title || chartConfig.title || 'Chart Visualization', m.left, y);
      y += 10;

      if (chartConfig.subtitle) {
        doc.setFontSize(12);
        this.setFontSafe(doc, 'helvetica', 'italic');
        y = this.ensureSpace(doc, y, 8);
        doc.text(chartConfig.subtitle, m.left, y);
        y += 8;
      }
      
      this.setFontSafe(doc, 'helvetica', 'normal');
      doc.setFontSize(10);
      y += 5;

      // Draw a placeholder box to frame the chart data
      const startBoxY = y;
      doc.setDrawColor(220, 220, 220);
      doc.roundedRect(m.left, y, this.getContentWidth(doc), 100, 3, 3, 'D');
      y += 8;

      doc.text(`Chart Type: ${chartConfig.chartType}`, m.left + 5, y);
      y += 10;
      
      // Render chart data as text inside the box
      if ('data' in chartConfig && chartConfig.data) {
        const dataAsText = JSON.stringify(chartConfig.data.slice(0, 5), null, 2); // Show first 5 records
        const dataLines = doc.splitTextToSize(dataAsText, this.getContentWidth(doc) - 15);
        y = this.ensureSpace(doc, y, dataLines.length * 5);
        this.setFontSafe(doc, 'courier', 'normal');
        doc.setFontSize(8);
        doc.text(dataLines, m.left + 5, y);
        y += dataLines.length * 5;
        this.setFontSafe(doc, 'helvetica', 'normal');
        doc.setFontSize(10);
      } else if ('nodes' in chartConfig) { // For flow charts
          const nodesText = `Nodes: ${chartConfig.nodes.length}, Edges: ${chartConfig.edges.length}`;
          y = this.ensureSpace(doc, y, 7);
          doc.text(nodesText, m.left + 5, y);
          y += 7;
      }
      
      y = Math.max(y, startBoxY + 105); // Ensure y is outside the box
      y = this.ensureSpace(doc, y, 10);

      if (chartConfig.explanation) {
        y += 5;
        doc.setFontSize(10);
        this.setFontSafe(doc, 'helvetica', 'italic');
        const explanationLines = doc.splitTextToSize(`Explanation: ${chartConfig.explanation}`, this.getContentWidth(doc));
        doc.text(explanationLines, m.left, y);
        y += explanationLines.length * 7;
      }

      return y + 10;
    } catch (error) {
      console.error('Error rendering chart in PDF:', error);
      const errorText = 'Chart visualization could not be rendered due to an error.';
      y = this.ensureSpace(doc, yPosition, 7);
      doc.text(errorText, m.left, y);
      return y + 10;
    }
  }
}

// Export a singleton instance
export const pdfGeneratorTool = new PdfGeneratorTool();

// ... The rest of the file (generatePDF function, MarkdownRendererTool, ChartTool, etc.) remains unchanged ...
// The following code is provided for context and completeness of the file, as it was in the original prompt.

/**
 * Generate a PDF from markdown content
 * @param options - PDF generation options
 * @returns - The PDF generation result
 */
export async function generatePDF(options: {
  title: string;
  content: string;
  fileName?: string;
  category?: string;
  metadata?: Record<string, string>;
}): Promise<{
  success: boolean;
  fileUrl?: string;
  error?: string;
}> {
  try {
    // Create PDF content
    const pdfContent: PdfContent[] = [
      {
        title: options.title,
        content: options.content
      }
    ];

    // Generate PDF
    const result = await pdfGeneratorTool.generatePdf(pdfContent, {
      title: options.title,
      saveToByteStore: true,
      category: options.category || 'PMO',
      documentType: 'Requirements Specification',
      includeCover: true,
      includeToc: true,
      ...options.metadata
    });

    // Check if result is a SavePdfToByteStoreResult
    if (typeof result === 'object' && 'downloadUrl' in result && typeof result.downloadUrl === 'string') {
      return {
        success: true,
        fileUrl: result.downloadUrl
      };
    }

    return {
      success: false,
      error: 'Failed to save PDF to byte store'
    };
  } catch (error: any) {
    console.error('Error generating PDF:', error);
    return {
      success: false,
      error: error.message || 'Failed to generate PDF'
    };
  }
}

/**
 * Markdown Renderer Tool
 *
 * This tool provides markdown processing functionality extracted from the MarkdownRenderer component.
 * It ensures consistent markdown rendering across the application, including PDFs.
 */

/**
 * Efficiently preprocesses markdown content with minimal transformations
 * Preserves indentation and structure while ensuring proper rendering
 *
 * @param {string} markdown - Raw markdown content
 * @return {string} Minimally processed markdown
 */
function preprocessMarkdown(markdown: string): string {
  // Ensure markdown is a string
  if (!markdown || typeof markdown !== 'string') return "";

  // Identify code blocks to protect them from processing
  const codeBlockRegex = /```[\s\S]*?```/g;
  const codeBlocks: string[] = [];

  // Temporarily replace code blocks with placeholders
  let processedMarkdown = markdown.replace(codeBlockRegex, match => {
    const placeholder = `CODE_BLOCK_${codeBlocks.length}`;
    codeBlocks.push(match);
    return placeholder;
  });

  // Single-pass essential formatting corrections
  processedMarkdown = processedMarkdown
    // Normalize line endings to ensure consistent processing
    .replace(/\r\n/g, '\n')
    // Replace 3+ consecutive newlines with exactly 2
    .replace(/\n{3,}/g, '\n\n')
    // Ensure headers are followed by blank lines
    .replace(/^(#{1,6}[^\n]*)\n(?!\n)/gm, '$1\n\n')
    // Ensure list items in the same list stay grouped together
    .replace(/^(\s*[-*+]\s[^\n]*)\n(?!\n|\s*[-*+])/gm, '$1\n\n');

  // Restore code blocks
  processedMarkdown = processedMarkdown.replace(/CODE_BLOCK_(\d+)/g, (_, index) =>
    codeBlocks[parseInt(index)]
  );

  return processedMarkdown.trim();
}

/**
 * Converts markdown to a format suitable for PDF rendering
 * Handles headers, lists, bold text, etc.
 *
 * @param {string} markdown - Raw markdown content
 * @return {string} Processed markdown ready for PDF rendering
 */
function markdownToPdfFormat(markdown: string): string {
  // First preprocess the markdown for consistent structure
  const processedMarkdown = preprocessMarkdown(markdown);

  // Enhanced processing for PDF rendering
  let pdfReadyMarkdown = processedMarkdown;

  // Identify code blocks to protect them from processing
  const codeBlockRegex = /```[\s\S]*?```/g;
  const codeBlocks: string[] = [];

  // Temporarily replace code blocks with placeholders
  pdfReadyMarkdown = pdfReadyMarkdown.replace(codeBlockRegex, match => {
    const placeholder = `CODE_BLOCK_${codeBlocks.length}`;
    codeBlocks.push(match);
    return placeholder;
  });

  // Convert inline code (backticks) to <code> tags (after removing fenced blocks)
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/`([^`]+)`/g, '<code>$1</code>');

  // Convert markdown tables to simple HTML table structure
  // Detect blocks with a header row, separator row, and one or more data rows
  const lines = pdfReadyMarkdown.split('\n');
  let i = 0;
  const out: string[] = [];
  while (i < lines.length) {
    const header = lines[i];
    const separator = lines[i + 1];
    const isTableHeader = header && header.includes('|');
    const isSeparator = separator && /^\s*\|?\s*:?-{3,}:?(\s*\|\s*:?-{3,}:?)*\s*\|?\s*$/.test(separator);
    if (isTableHeader && isSeparator) {
      // Collect table rows
      const rows: string[] = [];
      rows.push(header);
      rows.push(separator);
      let j = i + 2;
      while (j < lines.length && lines[j].includes('|')) {
        rows.push(lines[j]);
        j++;
      }
      // Build HTML table
      const cells = (row: string) => row
        .trim()
        .replace(/^\|/, '')
        .replace(/\|$/, '')
        .split('|')
        .map(c => c.trim());
      const headers = cells(rows[0]);
      const dataRows = rows.slice(2).map(r => cells(r));
      const tableHtml = [
        '<table>',
        '<thead><tr>' + headers.map(h => `<th>${h}</th>`).join('') + '</tr></thead>',
        '<tbody>' + dataRows.map(r => '<tr>' + r.map(c => `<td>${c}</td>`).join('') + '</tr>').join('') + '</tbody>',
        '</table>'
      ].join('');
      out.push(tableHtml);
      i = j;
      continue;
    }
    out.push(lines[i]);
    i++;
  }
  pdfReadyMarkdown = out.join('\n');

  // Process headings to ensure proper formatting
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, title) => {
    const level = hashes.length;
    return `<h${level}>${title}</h${level}>`;
  });

  // Process lists for better formatting
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/^(\s*)[-*+]\s+(.+)$/gm, (match, indent, content) => {
    return `${indent}• ${content}`;
  });

  // Process numbered lists
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/^(\s*)(\d+)\.\s+(.+)$/gm, (match, indent, num, content) => {
    return `${indent}${num}. ${content}`;
  });

  // Process bold text
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/(\*\*|__)(.*?)\1/g, '<strong>$2</strong>');

  // Process italic text
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/(\*|_)(.*?)\1/g, '<em>$2</em>');

  // Process links and expand with URL in parentheses for PDF readability
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a> ($2)');

  // Process blockquotes
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/^>\s+(.+)$/gm, '<blockquote>$1</blockquote>');

  // Restore code blocks with formatting
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/CODE_BLOCK_(\d+)/g, (_, index) => {
    const codeBlock = codeBlocks[parseInt(index)];
    const code = codeBlock.replace(/```(?:\w+)?\n?([\s\S]*?)```/g, '$1').trim();
    const escapedCode = code
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
    return `<pre>${escapedCode}</pre>`;
  });

  return pdfReadyMarkdown;
}

/**
 * Extracts plain text from markdown for search indexing
 * Removes markdown syntax while preserving content
 *
 * @param {string} markdown - Raw markdown content
 * @return {string} Plain text without markdown syntax
 */
function markdownToPlainText(markdown: string): string {
  // First preprocess the markdown
  let processedMarkdown = preprocessMarkdown(markdown);

  // Remove markdown syntax
  processedMarkdown = processedMarkdown
    // Remove headers
    .replace(/^#{1,6}\s+(.+)$/gm, '$1')
    // Remove bold/italic
    .replace(/(\*\*|__)(.*?)\1/g, '$2')
    .replace(/(\*|_)(.*?)\1/g, '$2')
    // Remove code blocks
    .replace(/```[\s\S]*?```/g, '')
    .replace(/`([^`]+)`/g, '$1')
    // Remove links but keep text
    .replace(/\[([^\]]+)\]\([^\)]+\)/g, '$1')
    // Remove images
    .replace(/!\[([^\]]+)\]\([^\)]+\)/g, '$1')
    // Remove blockquotes
    .replace(/^>\s+(.+)$/gm, '$1')
    // Remove list markers
    .replace(/^(\s*)[-*+]\s+/gm, '$1')
    .replace(/^(\s*)\d+\.\s+/gm, '$1');

  return processedMarkdown.trim();
}

/**
 * Identifies and extracts sections from markdown content
 * Useful for creating a table of contents or section navigation
 *
 * @param {string} markdown - Raw markdown content
 * @return {Array<{level: number, title: string, content: string}>} Array of sections
 */
function extractMarkdownSections(markdown: string): Array<{level: number, title: string, content: string}> {
  const processedMarkdown = preprocessMarkdown(markdown);
  const lines = processedMarkdown.split('\n');
  const sections: Array<{level: number, title: string, content: string}> = [];

  let currentSection: {level: number, title: string, content: string} | null = null;
  let contentBuffer: string[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);

    if (headerMatch) {
      // If we have a current section, save it before starting a new one
      if (currentSection) {
        currentSection.content = contentBuffer.join('\n');
        sections.push(currentSection);
        contentBuffer = [];
      }

      // Start a new section
      currentSection = {
        level: headerMatch[1].length,
        title: headerMatch[2],
        content: ''
      };
    } else if (currentSection) {
      // Add to current section's content
      contentBuffer.push(line);
    }
  }

  // Don't forget the last section
  if (currentSection) {
    currentSection.content = contentBuffer.join('\n');
    sections.push(currentSection);
  }

  return sections;
}

export class MarkdownRendererTool {
  /**
   * Process markdown for consistent rendering
   */
  preprocessMarkdown(markdown: string): string {
    return preprocessMarkdown(markdown);
  }

  /**
   * Format markdown for PDF rendering
   */
  markdownToPdfFormat(markdown: string): string {
    return markdownToPdfFormat(markdown);
  }

  /**
   * Convert markdown to plain text
   */
  markdownToPlainText(markdown: string): string {
    return markdownToPlainText(markdown);
  }

  /**
   * Extract sections from markdown
   */
  extractSections(markdown: string): Array<{level: number, title: string, content: string}> {
    return extractMarkdownSections(markdown);
  }

  /**
   * Process markdown content with the specified operation
   * @param options - Processing options
   * @returns Processed markdown
   */
  async process(options: {
    markdown: string;
    operation: 'preprocess' | 'toPdfFormat' | 'toPlainText' | 'extractSections';
  }): Promise<{
    success: boolean;
    content: string;
    sections?: Array<{level: number, title: string, content: string}>;
    error?: string;
  }> {
    try {
      const { markdown, operation } = options;

      if (!markdown) {
        return {
          success: false,
          content: '',
          error: 'Markdown content is required'
        };
      }

      switch (operation) {
        case 'preprocess':
          return {
            success: true,
            content: this.preprocessMarkdown(markdown)
          };

        case 'toPdfFormat':
          return {
            success: true,
            content: this.markdownToPdfFormat(markdown)
          };

        case 'toPlainText':
          return {
            success: true,
            content: this.markdownToPlainText(markdown)
          };

        case 'extractSections':
          const sections = this.extractSections(markdown);
          return {
            success: true,
            content: JSON.stringify(sections),
            sections
          };

        default:
          return {
            success: false,
            content: '',
            error: `Unknown operation: ${operation}`
          };
      }
    } catch (error) {
      console.error('Error in markdown renderer process:', error);
      return {
        success: false,
        content: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Tool definition for function calling
   * This can be used in the tools array when calling Groq API
   */
  getToolDefinition() {
    return {
      type: "function",
      function: {
        name: "markdownRenderer",
        description: "Process and format markdown content for various display purposes",
        parameters: {
          type: "object",
          properties: {
            markdown: {
              type: "string",
              description: "The markdown content to process"
            },
            operation: {
              type: "string",
              description: "The operation to perform on the markdown",
              enum: ["preprocess", "toPdfFormat", "toPlainText", "extractSections"]
            }
          },
          required: ["markdown", "operation"]
        }
      }
    };
  }
}

// Export a singleton instance
export const markdownRendererTool = new MarkdownRendererTool();

/**
 * Chart Generation Tool for creating data visualizations from text prompts
 * Uses LLM to convert natural language to chart data and configuration
 *
 * Note: This file contains several type definitions that are not directly used
 * in the code but are kept for documentation and type inference purposes.
 */

import { LlmTool, LlmProvider } from './llm-tool';
import { z } from 'zod';

// Initialize the LLM tool for processing prompts
const llmTool = new LlmTool();

/**
 * Chart types supported by the tool
 */
export const CHART_TYPES = {
  BAR: 'bar',
  LINE: 'line',
  PIE: 'pie',
  AREA: 'area',
  SCATTER: 'scatter',
  RADAR: 'radar',
  COMPOSED: 'composed',
  TABLE: 'table',
  FLOW: 'flow',
  HEATMAP: 'heatmap',
  BUBBLE: 'bubble'
} as const;

export type ChartType = typeof CHART_TYPES[keyof typeof CHART_TYPES];

/**
 * Zod schemas for chart validation
 */

// Schema for axis configuration
const AxisSchema = z.object({
  label: z.string(),
  dataKey: z.string().optional(),
});

type AxisConfig = z.infer<typeof AxisSchema>;

// Schema for table column
const TableColumnSchema = z.object({
  header: z.string(),
  accessorKey: z.string(),
  type: z.enum(['string', 'number', 'date', 'boolean']),
  // Add aliases for compatibility with different naming conventions
  title: z.string().optional(),
  dataKey: z.string().optional(),
});

type TableColumn = z.infer<typeof TableColumnSchema>;

// Base chart schema with common properties for all chart types
const BaseChartSchema = z.object({
  chartType: z.enum(['bar', 'line', 'pie', 'area', 'scatter', 'radar', 'composed', 'table', 'flow', 'heatmap', 'bubble'] as [string, ...string[]]),
  title: z.string(),
  subtitle: z.string().optional(),
  colors: z.array(z.string()).optional(),
  legend: z.boolean().optional(),
  tooltip: z.boolean().optional(),
  grid: z.boolean().optional(),
  explanation: z.string().optional(),
});

type BaseChartConfig = z.infer<typeof BaseChartSchema>;

// Schema for standard chart types (bar, line, pie, area, radar)
const StandardChartSchema = BaseChartSchema.extend({
  data: z.array(
    z.record(z.string(), z.union([z.string(), z.number()]))
  ).min(1),
  xAxis: AxisSchema.optional(),
  yAxis: AxisSchema.optional(),
}).refine(data => {
  // For standard charts, ensure data is properly structured
  return data.chartType !== CHART_TYPES.SCATTER;
}, {
  message: "Standard chart schema cannot be used for scatter plots",
  path: ["chartType"],
});

type StandardChartConfig = z.infer<typeof StandardChartSchema>;

// Schema specifically for scatter plots
const ScatterChartSchema = BaseChartSchema.extend({
  chartType: z.literal(CHART_TYPES.SCATTER),
  data: z.array(
    z.object({
      name: z.string().optional(),
      x: z.number(),
      y: z.number(),
    })
  ).min(1),
  xAxis: AxisSchema.optional(),
  yAxis: AxisSchema.optional(),
});

type ScatterChartConfig = z.infer<typeof ScatterChartSchema>;

// Schema specifically for table visualizations
const TableChartSchema = BaseChartSchema.extend({
  chartType: z.literal(CHART_TYPES.TABLE),
  columns: z.array(TableColumnSchema).min(1),
  data: z.array(z.record(z.string(), z.union([z.string(), z.number(), z.boolean(), z.date()]))).min(1),
  pagination: z.boolean().optional(),
  rowsPerPage: z.number().optional(),
});

type TableChartConfig = z.infer<typeof TableChartSchema>;

// Schema for flow chart nodes
const FlowNodeSchema = z.object({
  id: z.string(),
  type: z.string().optional(),
  position: z.object({
    x: z.number(),
    y: z.number()
  }),
  data: z.object({
    label: z.string(),
    description: z.string().optional(),
  }).optional(),
  style: z.record(z.string(), z.union([z.string(), z.number()])).optional(),
});

type FlowNode = z.infer<typeof FlowNodeSchema>;

// Schema for flow chart edges
const FlowEdgeSchema = z.object({
  id: z.string(),
  source: z.string(),
  target: z.string(),
  label: z.string().optional(),
  type: z.string().optional(),
  animated: z.boolean().optional(),
  style: z.record(z.string(), z.union([z.string(), z.number()])).optional(),
});

type FlowEdge = z.infer<typeof FlowEdgeSchema>;

// Schema specifically for flow charts
const FlowChartSchema = BaseChartSchema.extend({
  chartType: z.literal(CHART_TYPES.FLOW),
  nodes: z.array(FlowNodeSchema).min(1),
  edges: z.array(FlowEdgeSchema).min(0),
  direction: z.enum(['LR', 'RL', 'TB', 'BT']).optional(),
  fitView: z.boolean().optional(),
});

type FlowChartConfig = z.infer<typeof FlowChartSchema>;

// Schema specifically for heat map charts
const HeatMapChartSchema = BaseChartSchema.extend({
  chartType: z.literal(CHART_TYPES.HEATMAP),
  data: z.array(z.record(z.string(), z.union([z.string(), z.number()]))).min(1),
  xAxis: AxisSchema.optional(),
  yAxis: AxisSchema.optional(),
  colorScale: z.array(z.string()).optional(),
  showValues: z.boolean().optional(),
});

type HeatMapChartConfig = z.infer<typeof HeatMapChartSchema>;

// Schema specifically for bubble charts
const BubbleChartSchema = BaseChartSchema.extend({
  chartType: z.literal(CHART_TYPES.BUBBLE),
  data: z.array(
    z.object({
      name: z.string().optional(),
      x: z.number(),
      y: z.number(),
      z: z.number(),
      category: z.string().optional(),
    })
  ).min(1),
  xAxis: AxisSchema.optional(),
  yAxis: AxisSchema.optional(),
  zAxis: z.object({
    label: z.string().optional(),
    dataKey: z.string().optional(),
    range: z.tuple([z.number(), z.number()]).optional(),
  }).optional(),
  colorKey: z.string().optional(),
});

type BubbleChartConfig = z.infer<typeof BubbleChartSchema>;

// Combined chart schema that can be any of the specific chart types
const ChartConfigSchema = z.union([
  StandardChartSchema,
  ScatterChartSchema,
  TableChartSchema,
  FlowChartSchema,
  HeatMapChartSchema,
  BubbleChartSchema
]);

export type ChartConfig = z.infer<typeof ChartConfigSchema>;

// Types for chart generation options
export interface ChartGenerationOptions {
  prompt: string;
  chartType?: ChartType;
  model?: string;
  provider?: LlmProvider;
}

// Types for chart generation result
export interface ChartGenerationResult {
  success: boolean;
  chartConfig?: ChartConfig;
  rawResponse?: string;
  cleaned?: boolean;
  error?: string;
}

// Types for LLM model options
// Using the ModelOptions from llm-tool.ts

/**
 * Chart Generation Tool class
 */
export class ChartTool {
  /**
   * Static description of the tool and its usage
   * This helps AI agents understand how to use the tool effectively
   */
  static description = {
    name: "generateChart",
    description: "Generate data visualizations from natural language descriptions.",
    parameters: {
      type: "object",
      properties: {
        prompt: {
          type: "string",
          description: "Natural language description of the chart to generate."
        },
        chartType: {
          type: "string",
          enum: Object.values(CHART_TYPES),
          description: "Optional specific chart type to generate. If not provided, the LLM will determine the best chart type."
        },
        model: {
          type: "string",
          description: "The LLM model to use for processing the prompt.",
          default: "gpt-4o"
        },
        provider: {
          type: "string",
          description: "The LLM provider to use.",
          default: "openai"
        }
      },
      required: ["prompt"]
    }
  };

  /**
   * Generate a chart from a natural language prompt
   * @param options - Chart generation options
   * @returns - Chart configuration and data
   */
  async generateChart(options: ChartGenerationOptions): Promise<ChartGenerationResult> {
    try {
      const {
        prompt,
        chartType,
        model = "gpt-4o",
        provider = "openai"
      } = options;

      if (!prompt) {
        throw new Error("Prompt is required");
      }

      // Create a system prompt that instructs the LLM how to generate chart data
      const systemPrompt = this._createSystemPrompt(chartType);

      // Process the prompt with the LLM
      const llmResponse = await llmTool.processContent({
        prompt,
        context: systemPrompt,
        model,
        provider,
        modelOptions: {
          temperature: 0.7,
          maxTokens: 2000
        }
      });

      // Debug output for raw LLM response
      console.log("Raw LLM response:", llmResponse);

      // Parse the LLM response to extract chart configuration
      let parsedConfig = this._parseChartResponse(llmResponse);

      // Debug output for parsed config
      console.log("Parsed config:", JSON.stringify(parsedConfig, null, 2));

      // Validate the chart configuration using Zod schema
      try {
        const validatedConfig = this._validateChartConfig(parsedConfig);
        console.log("Validated config:", JSON.stringify(validatedConfig, null, 2));
        return {
          success: true,
          chartConfig: validatedConfig,
          rawResponse: llmResponse
        };
      } catch (validationError: any) {
        console.log("Validation error, attempting LLM-based cleanup:", validationError.message);
        // If validation fails, try to fix the JSON using LLM
        const cleanedConfig = await this._cleanupChartConfigWithLLM(parsedConfig, prompt, model, provider);
        // Validate again after cleanup
        const validatedConfig = this._validateChartConfig(cleanedConfig);
        return {
          success: true,
          chartConfig: validatedConfig,
          rawResponse: llmResponse,
          cleaned: true
        };
      }
    } catch (error: any) {
      console.error("Error generating chart:", error);
      return {
        success: false,
        error: error.message || "Failed to generate chart"
      };
    }
  }

  /**
   * Use LLM to validate and fix chart configuration
   * @param chartConfig - Parsed but potentially invalid chart config
   * @param originalPrompt - The original user prompt
   * @param model - LLM model to use for cleanup
   * @param provider - Provider to use for cleanup
   * @returns - Cleaned up chart configuration
   */
  async _cleanupChartConfigWithLLM(chartConfig: any, originalPrompt: string, model: string, provider: string): Promise<any> {
    console.log("Using LLM to clean up chart configuration");

    // Create a system prompt specifically for cleanup
    const cleanupSystemPrompt = `You are a JSON validation and repair expert.
    You will be given a JSON object that represents a chart configuration, but it may have issues.
    Your task is to fix any problems with the JSON while preserving the original intent.

    The original user request was: "${originalPrompt}"

    Here is the current chart configuration that needs to be fixed:
    ${JSON.stringify(chartConfig, null, 2)}

    Please analyze this configuration and fix any issues, such as:
    1. Missing required fields (chartType, title, data)
    2. Incorrect data structure for the specified chart type
    3. Invalid or inconsistent data values
    4. Structural problems that would prevent rendering

    Return ONLY the fixed JSON object with no additional text or explanation.
    Ensure the JSON is valid and follows the schema requirements for the chart type.
    `;

    // Use a different model for cleanup if the original model failed
    const cleanupModel = model === "gpt-4o" ? "claude-sonnet-4-0" : "gpt-4o";
    const cleanupProvider: LlmProvider = provider === "openai" ? "anthropic" : "openai";

    try {
      const cleanupResponse = await llmTool.processContent({
        prompt: "Fix this chart configuration JSON",
        context: cleanupSystemPrompt,
        model: cleanupModel,
        provider: cleanupProvider,
        modelOptions: {
          temperature: 0.1, // Lower temperature for more deterministic output
          maxTokens: 2000
        }
      });

      // Parse the cleaned up response
      const cleanedConfig = this._parseChartResponse(cleanupResponse);
      console.log("Successfully cleaned up chart configuration with LLM");
      return cleanedConfig;
    } catch (cleanupError) {
      console.error("LLM cleanup failed:", cleanupError);
      // If cleanup fails, return the original config and let the caller handle it
      return chartConfig;
    }
  }

  /**
   * Create a system prompt for the LLM based on the requested chart type
   * @param chartType - Optional specific chart type
   * @returns - System prompt for the LLM
   */
  _createSystemPrompt(chartType?: ChartType): string {
    const availableChartTypes = Object.values(CHART_TYPES).join(', ');

    return `You are a data visualization expert. Your task is to convert a natural language description into a complete chart configuration that can be rendered with Recharts, a React charting library.

${chartType ? `The user has requested a ${chartType} chart specifically.` : `Choose the most appropriate chart type from: ${availableChartTypes}.`}

Analyze the user's request and generate a complete JSON response with the following structure:

{
  "chartType": "the chart type (${availableChartTypes})",
  "title": "chart title",
  "subtitle": "optional subtitle",
  "data": [
    // Array of data points with appropriate structure for the chart type
    // For example, for a bar chart:
    { "name": "Category 1", "value": 100 },
    { "name": "Category 2", "value": 200 }
  ],
  "xAxis": {
    "label": "x-axis label",
    "dataKey": "the key in data objects to use for x-axis"
  },
  "yAxis": {
    "label": "y-axis label"
  },
  "colors": ["#hexcolor1", "#hexcolor2"], // Optional array of colors
  "legend": true, // Whether to show a legend
  "tooltip": true, // Whether to show tooltips
  "grid": true, // Whether to show grid lines
  "explanation": "A brief explanation of why this chart type was chosen and what insights it reveals"
}

IMPORTANT: YOUR RESPONSE WILL BE VALIDATED AGAINST A STRICT SCHEMA

JSON FORMATTING AND VALIDATION RULES:
1. Use double quotes for all strings and property names
2. Do not use trailing commas in arrays or objects
3. Ensure all brackets and braces are properly closed and matched
4. Do not include comments in the final JSON
5. Do not include any text outside the JSON object
6. Ensure all array elements are separated by commas
7. Ensure all object properties are separated by commas
8. DO NOT wrap the JSON in markdown code blocks or backtick tags
9. Return ONLY the raw JSON object with no additional formatting
10. All required fields must be present and have the correct types
11. For scatter plots, each data point MUST have numeric x and y properties
12. For tables, all column accessorKeys must exist in every data row
13. DO NOT include any explanatory text before or after the JSON
14. DO NOT include phrases like "Here's the JSON:" or "The chart configuration is:"
15. ONLY RETURN THE RAW JSON OBJECT AND NOTHING ELSE

For different chart types, adjust the data structure appropriately:
- Bar charts: data with name/category and value(s)
  Example: [{ "name": "Category 1", "value": 100 }, { "name": "Category 2", "value": 200 }]

- Line charts: data with x values (often time-based) and y values
  Example: [{ "name": "Jan", "value": 100 }, { "name": "Feb", "value": 200 }]

- Pie charts: data with name and value (representing portions of a whole)
  Example: [{ "name": "Segment 1", "value": 30 }, { "name": "Segment 2", "value": 70 }]

- Area charts: similar to line charts but with filled areas
  Example: [{ "name": "Jan", "value": 100 }, { "name": "Feb", "value": 200 }]

- Scatter plots: data with x and y coordinates
  Example: [{ "name": "Point 1", "x": 10, "y": 20 }, { "name": "Point 2", "x": 30, "y": 40 }]
  IMPORTANT: For scatter plots, each data point MUST have 'x' and 'y' properties as numeric values

- Radar charts: data with multiple dimensions/metrics
  Example: [{ "name": "Metric 1", "value": 80 }, { "name": "Metric 2", "value": 60 }]

- Composed charts: combination of multiple chart types (specify in configuration)
  Example: [{ "name": "Jan", "bar": 100, "line": 80, "area": 60 }, { "name": "Feb", "bar": 200, "line": 150, "area": 120 }]

- Flow charts: nodes and edges representing a directed graph or process flow
  Example structure:
  {
    "chartType": "flow",
    "title": "Simple Process Flow",
    "nodes": [
      { "id": "1", "position": { "x": 0, "y": 0 }, "data": { "label": "Start", "description": "Process begins here" } },
      { "id": "2", "position": { "x": 200, "y": 0 }, "data": { "label": "Process A" } },
      { "id": "3", "position": { "x": 400, "y": 0 }, "data": { "label": "End" } }
    ],
    "edges": [
      { "id": "e1-2", "source": "1", "target": "2", "label": "Next step" },
      { "id": "e2-3", "source": "2", "target": "3", "label": "Complete", "animated": true }
    ],
    "direction": "LR",
    "fitView": true,
    "explanation": "This flow chart shows a simple linear process."
  }
  IMPORTANT: For flow charts, each node must have a unique id and position, and each edge must have source and target ids that match existing nodes

- Heat maps: data visualized as a matrix with color intensity
  Example structure:
  {
    "chartType": "heatmap",
    "title": "Sales Performance by Region and Quarter",
    "data": [
      { "name": "North", "Q1": 45, "Q2": 62, "Q3": 78, "Q4": 56 },
      { "name": "South", "Q1": 52, "Q2": 43, "Q3": 36, "Q4": 49 },
      { "name": "East", "Q1": 38, "Q2": 43, "Q3": 71, "Q4": 86 },
      { "name": "West", "Q1": 74, "Q2": 81, "Q3": 79, "Q4": 92 }
    ],
    "xAxis": { "dataKey": "name", "label": "Region" },
    "colorScale": ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"],
    "showValues": true,
    "explanation": "This heat map shows sales performance across regions and quarters, with darker colors indicating higher values."
  }
  IMPORTANT: For heat maps, data should be structured as a matrix with rows and columns, where each cell contains a numeric value

- Bubble charts: scatter plots with an additional dimension shown by bubble size
  Example structure:
  {
    "chartType": "bubble",
    "title": "Product Comparison",
    "data": [
      { "name": "Product A", "x": 65, "y": 78, "z": 120, "category": "Electronics" },
      { "name": "Product B", "x": 42, "y": 53, "z": 80, "category": "Electronics" },
      { "name": "Product C", "x": 78, "y": 32, "z": 150, "category": "Home" },
      { "name": "Product D", "x": 55, "y": 47, "z": 200, "category": "Home" }
    ],
    "xAxis": { "label": "Price ($)", "dataKey": "x" },
    "yAxis": { "label": "Customer Rating" },
    "zAxis": { "label": "Sales Volume", "dataKey": "z", "range": [20, 100] },
    "colorKey": "category",
    "explanation": "This bubble chart compares products by price (x-axis), rating (y-axis), and sales volume (bubble size)."
  }
  IMPORTANT: For bubble charts, each data point must have x, y, and z values, where z determines the bubble size

- Tables: tabular data with columns and rows
  Example structure:
  {
    "chartType": "table",
    "title": "Sales Data by Region",
    "columns": [
      { "header": "Region", "accessorKey": "region", "type": "string", "title": "Region", "dataKey": "region" },
      { "header": "Q1 Sales", "accessorKey": "q1", "type": "number", "title": "Q1 Sales", "dataKey": "q1" },
      { "header": "Q2 Sales", "accessorKey": "q2", "type": "number", "title": "Q2 Sales", "dataKey": "q2" }
    ],
    "data": [
      { "region": "North", "q1": 12500, "q2": 14200 },
      { "region": "South", "q1": 9800, "q2": 10600 },
      { "region": "East", "q1": 15200, "q2": 16100 },
      { "region": "West", "q1": 8900, "q2": 9300 }
    ],
    "pagination": true,
    "rowsPerPage": 10,
    "explanation": "This table shows quarterly sales data by region."
  }

  IMPORTANT FOR TABLES:
  - ALWAYS include the "columns" property with proper headers for each column
  - Make column headers clear, concise, and descriptive
  - Use proper capitalization for column headers
  - For number columns, specify "type": "number" to ensure proper formatting
  - The "accessorKey" in each column must match the property name in the data objects
  - CRITICAL: Each column MUST have "accessorKey", "header", and "type" properties exactly as shown in the example
  - CRITICAL: Include both "title" and "dataKey" properties that match "header" and "accessorKey" respectively for compatibility

Use realistic, plausible data that matches the user's request. If the user doesn't specify exact values, create reasonable sample data that illustrates the concept they're asking for.

Ensure your response is valid JSON that can be parsed directly. Do not include any text outside the JSON object.`;
  }

  /**
   * Parse the LLM response to extract chart configuration
   * @param response - Raw LLM response
   * @returns - Parsed chart configuration
   */
  _parseChartResponse(response: string): any {
    try {
      console.log("Parsing LLM response:", response.substring(0, 100) + "...");

      // First, check for markdown code blocks with JSON
      const markdownJsonMatch = response.match(/```(?:json)?\s*([\s\S]*?)```/);
      if (markdownJsonMatch && markdownJsonMatch[1]) {
        try {
          console.log("Found markdown code block, attempting to parse");
          return JSON.parse(markdownJsonMatch[1]);
        } catch (markdownError) {
          console.error("Error parsing markdown JSON:", markdownError);
          // Continue to other parsing methods
        }
      }

      // Next, try to find a JSON object in the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        // If no JSON object is found, try to extract from text that might contain instructions
        const lines = response.split('\n');
        let jsonLines: string[] = [];
        let inJson = false;

        for (const line of lines) {
          if (line.trim().startsWith('{') && !inJson) {
            inJson = true;
            jsonLines.push(line);
          } else if (inJson) {
            jsonLines.push(line);
            if (line.trim().endsWith('}')) {
              break;
            }
          }
        }

        if (jsonLines.length > 0) {
          const extractedJson = jsonLines.join('\n');
          console.log("Extracted potential JSON from lines:", extractedJson.substring(0, 100) + "...");
          try {
            return JSON.parse(extractedJson);
          } catch (extractError) {
            console.error("Error parsing extracted JSON:", extractError);
          }
        }

        throw new Error("No valid JSON found in the response");
      }

      let jsonStr = jsonMatch[0];
      let chartConfig;

      try {
        // Try to parse the JSON directly
        chartConfig = JSON.parse(jsonStr);
        console.log("Successfully parsed JSON directly");
      } catch (parseError) {
        // If direct parsing fails, try to fix common JSON issues
        console.log("Initial JSON parsing failed, attempting to fix JSON:", (parseError as Error).message);

        // Fix trailing commas in arrays and objects
        jsonStr = jsonStr.replace(/,\s*([\]\}])/g, '$1');

        // Fix missing commas between array elements or object properties
        jsonStr = jsonStr.replace(/("[^"]*"|\d+)\s*(\[)/g, '$1,$2');
        jsonStr = jsonStr.replace(/(\])\s*("[^"]*")/g, '$1,$2');

        // Fix unquoted property names
        jsonStr = jsonStr.replace(/(\{|,)\s*([a-zA-Z0-9_]+)\s*:/g, '$1"$2":');

        // Fix single quotes used instead of double quotes
        jsonStr = jsonStr.replace(/'/g, '"');

        // Remove any non-ASCII characters that might cause issues
        jsonStr = jsonStr.replace(/[^\x00-\x7F]/g, '');

        // Remove any comments (both // and /* */ style)
        jsonStr = jsonStr.replace(/\/\/.*?\n/g, '\n');
        jsonStr = jsonStr.replace(/\/\*[\s\S]*?\*\//g, '');

        // Try parsing again with the fixed JSON
        try {
          chartConfig = JSON.parse(jsonStr);
          console.log("Successfully fixed and parsed JSON");
        } catch (fixedParseError) {
          // If still failing, try a more aggressive approach - use a JSON5 like approach
          console.log("Fixed JSON still failed to parse, using fallback method");

          // Last resort: Use Function constructor as a more lenient parser (similar to JSON5)
          // This is safe in this context as we're only parsing, not executing arbitrary code
          try {
            // Convert to a JavaScript object literal and evaluate it
            const fallbackParse = new Function('return ' + jsonStr);
            chartConfig = fallbackParse();
            console.log("Successfully parsed using fallback method");
          } catch (fallbackError) {
            console.error("All parsing methods failed", fallbackError);
            throw new Error(`Could not parse JSON after multiple attempts: ${(fallbackError as Error).message}`);
          }
        }
      }

      // Validate the chart configuration
      this._validateChartConfig(chartConfig);

      return chartConfig;
    } catch (error: any) {
      console.error("Error parsing chart response:", error);
      throw new Error(`Failed to parse chart configuration: ${error.message}`);
    }
  }

  /**
   * Validate the chart configuration using Zod schemas
   * @param config - Chart configuration to validate
   * @returns - Validated chart configuration
   * @throws {Error} If the configuration is invalid
   */
  _validateChartConfig(config: any): ChartConfig {
    try {
      // Parse and validate the configuration using the appropriate Zod schema
      const validatedConfig = ChartConfigSchema.parse(config);

      // Additional validation for specific chart types
      if (validatedConfig.chartType === CHART_TYPES.TABLE && 'columns' in validatedConfig && Array.isArray(validatedConfig.columns)) {
        // For tables, ensure all column accessorKeys exist in every data row
        const columnKeys = validatedConfig.columns.map(col => col.accessorKey);

        // Add title and dataKey properties for compatibility with different components
        validatedConfig.columns = validatedConfig.columns.map(col => ({
          ...col,
          title: col.title || col.header,
          dataKey: col.dataKey || col.accessorKey
        }));

        validatedConfig.data.forEach((row: any, idx: number) => {
          const rowKeys = Object.keys(row);
          const missingKeys = columnKeys.filter(key => !rowKeys.includes(key));
          if (missingKeys.length > 0) {
            throw new Error(`Table row at index ${idx} is missing properties: ${missingKeys.join(', ')}`);
          }
        });
      } else if (validatedConfig.chartType === CHART_TYPES.COMPOSED) {
        // For composed charts, ensure data has appropriate properties for each chart type
        const firstDataPoint = validatedConfig.data[0];
        const dataKeys = Object.keys(firstDataPoint).filter(key => key !== 'name');
        if (dataKeys.length === 0) {
          throw new Error('Composed chart must have at least one data series');
        }
      }

      return validated